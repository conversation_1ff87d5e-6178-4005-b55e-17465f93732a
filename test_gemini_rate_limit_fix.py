#!/usr/bin/env python3
"""
Test script to verify the Gemini API rate limit fixes.
This script tests the new model selection and retry mechanisms.
"""

import json
import os
import sys
import time
import google.generativeai as genai
from datetime import datetime

# Add the main directory to path to import functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """Load configuration from config.json"""
    config_file = "config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            return json.load(f)
    return {}

def load_api_keys():
    """Load API keys from api_keys.json"""
    api_keys_file = "api_keys.json"
    if os.path.exists(api_keys_file):
        with open(api_keys_file, 'r') as f:
            return json.load(f)
    return []

def test_model_selection():
    """Test different model versions"""
    print("🧪 Testing Model Selection Feature")
    print("=" * 50)
    
    config = load_config()
    api_keys = load_api_keys()
    
    if not api_keys:
        print("❌ No API keys found. Please add API keys first.")
        return False
    
    api_key = api_keys[0]["key"]
    
    # Test different models
    models_to_test = [
        "gemini-2.5-flash-lite",
        "gemini-2.5-flash", 
        "gemini-2.0-flash",
        "gemini-1.5-flash"
    ]
    
    for model_name in models_to_test:
        print(f"\n🔧 Testing model: {model_name}")
        try:
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel(model_name)
            
            start_time = time.time()
            response = model.generate_content("Hello, respond with just 'OK'")
            end_time = time.time()
            
            if response and response.text:
                print(f"✅ {model_name}: Success ({end_time - start_time:.2f}s)")
                print(f"   Response: {response.text.strip()}")
            else:
                print(f"❌ {model_name}: No response")
                
        except Exception as e:
            error_msg = str(e)
            if "429" in error_msg:
                print(f"⚠️ {model_name}: Rate limited - {error_msg}")
            else:
                print(f"❌ {model_name}: Error - {error_msg}")
    
    return True

def test_rate_limit_handling():
    """Test rate limit handling with rapid requests"""
    print("\n🧪 Testing Rate Limit Handling")
    print("=" * 50)
    
    api_keys = load_api_keys()
    if not api_keys:
        print("❌ No API keys found.")
        return False
    
    api_key = api_keys[0]["key"]
    genai.configure(api_key=api_key)
    
    # Use the fastest model for testing
    model = genai.GenerativeModel("gemini-2.5-flash-lite")
    
    # Make rapid requests to trigger rate limiting
    print("🚀 Making rapid requests to test rate limiting...")
    
    for i in range(5):
        try:
            print(f"Request {i+1}/5...", end=" ")
            start_time = time.time()
            response = model.generate_content(f"Test request {i+1}. Respond with just the number {i+1}.")
            end_time = time.time()
            
            if response and response.text:
                print(f"✅ Success ({end_time - start_time:.2f}s): {response.text.strip()}")
            else:
                print("❌ No response")
                
        except Exception as e:
            error_msg = str(e)
            if "429" in error_msg:
                print(f"⚠️ Rate limited: {error_msg}")
                print("   This is expected behavior - the fix should handle this in the main app")
            else:
                print(f"❌ Error: {error_msg}")
        
        # Small delay between requests
        time.sleep(0.5)
    
    return True

def test_config_update():
    """Test updating the model configuration"""
    print("\n🧪 Testing Configuration Update")
    print("=" * 50)
    
    config = load_config()
    
    # Test setting different model versions
    test_models = ["gemini-2.5-flash-lite", "gemini-2.5-flash", "gemini-2.0-flash"]
    
    for model_name in test_models:
        config["gemini_model_version"] = model_name
        
        # Save config
        with open("config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        # Reload and verify
        with open("config.json", 'r') as f:
            reloaded_config = json.load(f)
        
        if reloaded_config.get("gemini_model_version") == model_name:
            print(f"✅ Successfully set model to: {model_name}")
        else:
            print(f"❌ Failed to set model to: {model_name}")
    
    return True

def main():
    """Main test function"""
    print("🔬 Gemini API Rate Limit Fix Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    tests = [
        ("Model Selection", test_model_selection),
        ("Rate Limit Handling", test_rate_limit_handling),
        ("Configuration Update", test_config_update)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The rate limit fixes should work correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
