#!/usr/bin/env python3
"""
Test script to verify the Gemini API safety handling fixes.
This script tests the new response validation and safety block handling.
"""

import json
import os
import sys
import time
import google.generativeai as genai
from datetime import datetime

# Add the main directory to path to import functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """Load configuration from config.json"""
    config_file = "000/config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            return json.load(f)
    return {}

def load_api_keys():
    """Load API keys from api_keys.json"""
    api_keys_file = "api_keys.json"
    if os.path.exists(api_keys_file):
        with open(api_keys_file, 'r') as f:
            return json.load(f)
    return []

def test_response_validation():
    """Test the response validation function"""
    print("🧪 Testing Response Validation")
    print("=" * 50)
    
    # Import the validation function from Meta Master
    try:
        from Meta_Master import validate_gemini_response
        print("✅ Successfully imported validate_gemini_response function")
    except ImportError as e:
        print(f"❌ Failed to import validation function: {e}")
        return False
    
    # Test with mock responses
    class MockResponse:
        def __init__(self, text=None, finish_reason=None, has_candidates=True):
            self.text = text
            self.candidates = []
            if has_candidates:
                candidate = MockCandidate(finish_reason)
                self.candidates.append(candidate)
    
    class MockCandidate:
        def __init__(self, finish_reason=None):
            self.finish_reason = finish_reason
            self.content = MockContent()
    
    class MockContent:
        def __init__(self):
            self.parts = [MockPart()]
    
    class MockPart:
        def __init__(self):
            self.text = "Test response"
    
    # Test cases
    test_cases = [
        ("Normal response", MockResponse("Hello world", 1), True),
        ("Safety blocked", MockResponse(None, 2), False),
        ("Recitation blocked", MockResponse(None, 3), False),
        ("Other reason", MockResponse(None, 4), False),
        ("No candidates", MockResponse("Hello", None, False), False),
        ("Empty response", MockResponse("", 1), False),
    ]
    
    for test_name, mock_response, expected_valid in test_cases:
        is_valid, text_content, error_message = validate_gemini_response(mock_response)
        
        if is_valid == expected_valid:
            print(f"✅ {test_name}: {'Valid' if is_valid else 'Invalid'} - {error_message}")
        else:
            print(f"❌ {test_name}: Expected {'Valid' if expected_valid else 'Invalid'}, got {'Valid' if is_valid else 'Invalid'}")
    
    return True

def test_safety_with_real_api():
    """Test safety handling with real API calls"""
    print("\n🧪 Testing Safety Handling with Real API")
    print("=" * 50)
    
    api_keys = load_api_keys()
    if not api_keys:
        print("❌ No API keys found. Skipping real API tests.")
        return False
    
    config = load_config()
    api_key = api_keys[0]["key"]
    
    try:
        genai.configure(api_key=api_key)
        model_version = config.get("gemini_model_version", "gemini-2.5-flash-lite")
        model = genai.GenerativeModel(model_version)
        
        # Test with safe content
        print("🔍 Testing with safe content...")
        try:
            response = model.generate_content("Describe a beautiful sunset.")
            print(f"✅ Safe content: Success - {len(response.text)} characters")
        except Exception as e:
            print(f"❌ Safe content failed: {e}")
        
        # Test with potentially problematic content (but not actually harmful)
        print("🔍 Testing with edge case content...")
        try:
            response = model.generate_content("Write a short story about a detective.")
            print(f"✅ Edge case content: Success - {len(response.text)} characters")
        except Exception as e:
            if "safety" in str(e).lower():
                print(f"⚠️ Edge case blocked by safety: {e}")
            else:
                print(f"❌ Edge case failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_error_handling():
    """Test error handling for different scenarios"""
    print("\n🧪 Testing Error Handling")
    print("=" * 50)
    
    # Test with invalid API key
    print("🔍 Testing with invalid API key...")
    try:
        genai.configure(api_key="invalid_key_12345")
        model = genai.GenerativeModel("gemini-2.5-flash-lite")
        response = model.generate_content("Test")
        print("❌ Invalid API key should have failed")
    except Exception as e:
        if "403" in str(e) or "401" in str(e) or "invalid" in str(e).lower():
            print("✅ Invalid API key properly rejected")
        else:
            print(f"⚠️ Unexpected error with invalid key: {e}")
    
    # Test with empty prompt
    api_keys = load_api_keys()
    if api_keys:
        print("🔍 Testing with empty prompt...")
        try:
            genai.configure(api_key=api_keys[0]["key"])
            model = genai.GenerativeModel("gemini-2.5-flash-lite")
            response = model.generate_content("")
            print(f"⚠️ Empty prompt accepted: {response.text}")
        except Exception as e:
            print(f"✅ Empty prompt properly handled: {e}")
    
    return True

def main():
    """Main test function"""
    print("🔬 Gemini API Safety Handling Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    tests = [
        ("Response Validation", test_response_validation),
        ("Safety with Real API", test_safety_with_real_api),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The safety handling should work correctly.")
        print("\n💡 Key improvements:")
        print("   • Safety blocks are now properly detected and handled")
        print("   • Response validation prevents crashes from empty responses")
        print("   • Clear error messages help users understand what happened")
        print("   • Automatic retry with different API keys when possible")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
