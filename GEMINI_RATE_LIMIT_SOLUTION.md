# Gemini API Rate Limit Solution

## Problem Analysis

You were experiencing 429 rate limit errors with the Gemini API despite having quotas that showed you were within limits. This is a common issue that can occur due to several factors:

### Root Causes Identified:

1. **Burst Rate Limiting**: Even if overall quotas are fine, you might hit burst limits (requests per minute)
2. **Model-Specific Limits**: Different Gemini models have different rate limits
3. **Regional Rate Limiting**: Some regions have different limits
4. **Inadequate Retry Logic**: Missing proper exponential backoff
5. **API Key Rotation Issues**: Suboptimal key switching logic
6. **Safety Filter Blocks**: Response blocked by safety filters causing crashes
7. **Invalid Response Handling**: Missing validation for empty or blocked responses

## Solution Implemented

### 1. Model Selection in Settings

**Location**: Settings Window → "Gemini API Settings" section

**Available Models**:
- `gemini-2.5-flash-lite` - Fastest, 15 RPM free tier (Default)
- `gemini-2.5-flash` - Balanced, 10 RPM free tier  
- `gemini-2.0-flash` - Latest, 15 RPM free tier
- `gemini-1.5-flash` - Stable, 15 RPM free tier
- `gemini-1.5-pro` - Most capable, 5 RPM free tier

**How to Use**:
1. Open Settings (⚙️ button)
2. Scroll to "Gemini API Settings"
3. Select your preferred model
4. Click "Save Settings"

### 2. Advanced Retry Mechanism

**Features**:
- Exponential backoff with jitter
- Maximum 5 retry attempts
- Intelligent API key switching
- Configurable delays (1s to 60s max)

**Configuration**:
```python
MAX_RETRY_ATTEMPTS = 5
BASE_RETRY_DELAY = 1.0  # Base delay in seconds
MAX_RETRY_DELAY = 60.0  # Maximum delay in seconds
EXPONENTIAL_BASE = 2.0  # Exponential backoff multiplier
```

### 3. Improved Rate Limiting

**Enhanced Configuration**:
```python
RATE_LIMIT_WINDOW = 60  # 1 minute window
MAX_REQUESTS = 45       # Requests per minute
BURST_LIMIT = 5         # Burst allowance
MIN_DELAY = 0.05        # Minimum delay between requests
BATCH_SIZE = 5          # Batch processing size
```

### 4. Safety Filter Handling

**Features**:
- Automatic detection of safety-blocked responses
- Graceful handling of finish_reason codes
- Clear error messages for blocked content
- No crashes from empty responses

**Safety Codes Handled**:
- `STOP (1)` - Normal completion ✅
- `SAFETY (2)` - Blocked by safety filters ⚠️
- `RECITATION (3)` - Blocked due to recitation ⚠️
- `OTHER (4)` - Other blocking reason ⚠️

## Recommendations by Model

### For High Volume Processing:
- **Primary**: `gemini-2.5-flash-lite` (15 RPM, fastest)
- **Backup**: `gemini-2.0-flash` (15 RPM, latest features)

### For Quality-Critical Tasks:
- **Primary**: `gemini-2.5-flash` (10 RPM, balanced)
- **Backup**: `gemini-1.5-pro` (5 RPM, highest quality)

### For Stable Production:
- **Primary**: `gemini-1.5-flash` (15 RPM, proven stable)
- **Backup**: `gemini-2.5-flash-lite` (15 RPM, fast fallback)

## Usage Instructions

### Step 1: Configure Model
1. Open Meta Master
2. Click Settings (⚙️)
3. Go to "Gemini API Settings"
4. Select appropriate model based on your needs
5. Save settings

### Step 2: Monitor Performance
- Watch the API key status indicator
- Check console output for retry messages
- Monitor processing speed vs quality trade-offs

### Step 3: Optimize for Your Use Case
- **Speed Priority**: Use `gemini-2.5-flash-lite`
- **Quality Priority**: Use `gemini-1.5-pro` 
- **Balanced**: Use `gemini-2.5-flash`

## Troubleshooting

### Still Getting 429 Errors?

1. **Check Your Tier**: Verify you're on the correct billing tier
2. **Switch Models**: Try a model with higher RPM limits
3. **Add More API Keys**: The system will rotate between them
4. **Enable Batch Processing**: Reduces total API calls
5. **Increase Delays**: Modify rate limiting settings if needed

### Getting Safety Filter Errors?

1. **Try Different Images**: Some images may trigger safety filters
2. **Adjust Prompts**: Modify your custom prompt parts to be more neutral
3. **Use Different Models**: Some models have different safety thresholds
4. **Check Image Content**: Avoid images with potentially sensitive content

**Common Safety Triggers**:
- Images with people in certain contexts
- Medical or health-related imagery
- Images that could be interpreted as violent
- Copyrighted or branded content

### Model Not Working?

1. **Verify Model Name**: Ensure the model exists and is accessible
2. **Check API Key**: Some models require specific permissions
3. **Try Fallback**: Switch to `gemini-1.5-flash` (most compatible)

### Performance Issues?

1. **Monitor RPM Usage**: Check Google Cloud Console quotas
2. **Optimize Batch Size**: Larger batches = fewer API calls
3. **Use Faster Models**: `flash-lite` models are optimized for speed

## Technical Details

### Files Modified:
- `Meta Master.py` - Main application
- `config.json` - Configuration storage

### New Functions Added:
- `advanced_retry_with_backoff()` - Intelligent retry mechanism
- `validate_gemini_response()` - Response validation and safety handling
- Model selection UI in settings
- Enhanced error handling for all API calls

### Configuration Keys:
- `gemini_model_version` - Selected model name

## Testing

Run the test scripts to verify everything works:

```bash
# Test rate limiting and model selection
python test_gemini_rate_limit_fix.py

# Test safety handling and response validation
python test_safety_handling.py
```

This will test:
- Model selection functionality
- Rate limit handling
- Configuration updates
- Safety filter handling
- Response validation

## Best Practices

1. **Start with Flash Lite**: Begin with the fastest model
2. **Monitor Quotas**: Keep an eye on your usage in Google Cloud Console
3. **Use Multiple Keys**: Add several API keys for better throughput
4. **Enable Batch Processing**: Process multiple images per API call
5. **Upgrade Tier**: Consider paid tiers for higher limits

## Support

If you continue experiencing issues:

1. Check the console output for detailed error messages
2. Verify your Google Cloud billing and quotas
3. Try different model combinations
4. Consider upgrading to a paid tier for higher limits

The solution provides multiple layers of protection against rate limiting while giving you control over the speed vs quality trade-off through model selection.
